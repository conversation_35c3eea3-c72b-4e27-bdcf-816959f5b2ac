import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import {
  Bot,
  ChevronLeft,
  ChevronRight,
  FileText,
  Sparkles,
  CheckCircle,
  AlignLeft,
  BarChart3,
  Lightbulb,
  Target,
  AlertCircle,
  FlaskConical,
  TrendingUp,
  PenTool,
  Search,
  BookOpen,
  Type,
  Edit,
  FileCheck,
  Zap,
  Settings,
  List,
  Repeat,
  Wand2,
  ChevronDown,
  ChevronUp,
  Send
} from 'lucide-react';
import { toast } from 'sonner';
import { enhancedAIService, AI_MODELS, RESEARCH_TOOLS, AIModel, ResearchTool } from './paper-generator/enhanced-ai.service';
import './editor-sidebar.css'; // Import custom styling for editor/sidebar interaction

interface EditorAISidebarProps {
  wordCount: number;
  characterCount: number;
  status: string;
  lastSaved: Date | null;
  isVisible: boolean;
  onToggleVisibility: () => void;
  onAIRequest: (prompt: string, text: string, mode: 'replace' | 'insert' | 'display') => void;
  onDocumentAIRequest: (prompt: string, toolId: string) => void;
  selectedText: string;
  documentContent: string;
  aiLoading: boolean;
  aiResponse: string;
}

export function EditorAISidebar({
  wordCount,
  characterCount,
  status,
  lastSaved,
  isVisible,
  onToggleVisibility,
  onAIRequest,
  onDocumentAIRequest,
  selectedText,
  documentContent,
  aiLoading,
  aiResponse
}: EditorAISidebarProps) {
  const [selectedModel, setSelectedModel] = useState<string>(enhancedAIService.getDefaultModel());
  const [localVisible, setLocalVisible] = useState<boolean>(isVisible);
  const [customPrompt, setCustomPrompt] = useState<string>('');

  // Collapsible sections state
  const [showQuickActions, setShowQuickActions] = useState(true);
  const [showContentGeneration, setShowContentGeneration] = useState(false);
  const [showTextEnhancement, setShowTextEnhancement] = useState(false);
  const [showResearchTools, setShowResearchTools] = useState(false);

  // Sync local state with prop
  useEffect(() => {
    setLocalVisible(isVisible);
  }, [isVisible]);

  // Handle local toggle
  const handleToggleVisibility = () => {
    const newState = !localVisible;
    setLocalVisible(newState);
    onToggleVisibility();
  };

  // Handle custom prompt execution
  const handleCustomPromptExecution = async () => {
    if (!customPrompt.trim()) return;

    try {
      const result = await enhancedAIService.generateText(
        customPrompt.trim(),
        selectedModel,
        { maxTokens: 2048, temperature: 0.7 }
      );
      onAIRequest('', result, 'display');
      setCustomPrompt('');
      toast.success('AI prompt executed successfully');
    } catch (error: any) {
      console.error('Custom prompt error:', error);
      toast.error(error.message || 'Failed to execute AI prompt');
    }
  };

  // Handle AI tool execution
  const handleToolExecution = async (tool: ResearchTool) => {
    if (tool.requiresSelection && !selectedText.trim()) {
      toast.error(`Please select text to use "${tool.name}"`);
      return;
    }

    try {
      const context = tool.requiresSelection ? selectedText : documentContent;

      // For quick enhancement tools, use the optimized method
      if (['improve-clarity', 'fix-grammar', 'academic-tone', 'expand-point'].includes(tool.id)) {
        const enhancementType = tool.id.replace('improve-', '').replace('fix-', '').replace('-tone', '').replace('-point', '') as 'clarity' | 'grammar' | 'academic' | 'expand';
        const result = await enhancedAIService.quickEnhance(selectedText, enhancementType, selectedModel);
        onAIRequest('', result, tool.mode);
      } else {
        // For other tools, use the full research tool execution
        const result = await enhancedAIService.executeResearchTool(tool.id, context, selectedModel);

        if (tool.mode === 'display') {
          onAIRequest('', result, 'display');
        } else {
          onAIRequest('', result, tool.mode);
        }
      }

      toast.success(`${tool.name} completed successfully`);
    } catch (error: any) {
      console.error('Tool execution error:', error);
      toast.error(error.message || `Failed to execute ${tool.name}`);
    }
  };

  // Get tools by category
  const getToolsByCategory = (category: string): ResearchTool[] => {
    return RESEARCH_TOOLS.filter(tool => tool.category === category);
  };

  // Get model by category
  const getModelsByCategory = (category: string): AIModel[] => {
    if (category === 'all') return AI_MODELS;
    return AI_MODELS.filter(model => model.category === category);
  };

  // Icon mapping for tools
  const iconMap: Record<string, React.ReactNode> = {
    Type: <Type className="h-4 w-4" />,
    Target: <Target className="h-4 w-4" />,
    BookOpen: <BookOpen className="h-4 w-4" />,
    Sparkles: <Sparkles className="h-4 w-4" />,
    FlaskConical: <FlaskConical className="h-4 w-4" />,
    Lightbulb: <Lightbulb className="h-4 w-4" />,
    Search: <Search className="h-4 w-4" />,
    TrendingUp: <TrendingUp className="h-4 w-4" />,
    CheckCircle: <CheckCircle className="h-4 w-4" />,
    FileText: <FileText className="h-4 w-4" />,
    BarChart3: <BarChart3 className="h-4 w-4" />,
    FileCheck: <FileCheck className="h-4 w-4" />,
    Edit: <Edit className="h-4 w-4" />,
    PenTool: <PenTool className="h-4 w-4" />,
    AlignLeft: <AlignLeft className="h-4 w-4" />,
    List: <List className="h-4 w-4" />,
    Repeat: <Repeat className="h-4 w-4" />
  };

  return (
    <>
      {/* Enhanced Toggle Button - Always visible on the right edge with improved design */}
      <div className="fixed right-0 top-1/2 transform -translate-y-1/2 z-50">
        <Button
          variant="outline"
          size="sm"
          onClick={handleToggleVisibility}
          className={`rounded-l-lg rounded-r-none border-r-0 bg-white hover:bg-blue-50 transition-all duration-300 shadow-lg border-blue-200 ${
            localVisible ? 'translate-x-0 bg-blue-50' : 'translate-x-0'
          }`}
          title={localVisible ? "Hide AI Assistant" : "Show AI Assistant"}
        >
          {localVisible ? (
            <div className="flex items-center">
              <ChevronRight className="h-4 w-4 text-blue-600" />
            </div>
          ) : (
            <div className="flex items-center space-x-1">
              <Bot className="h-4 w-4 text-blue-600" />
              <ChevronLeft className="h-4 w-4 text-blue-600" />
            </div>
          )}
        </Button>
      </div>

      {/* Main Sidebar */}
      <div className={`ai-sidebar-overlay ${localVisible ? 'visible' : ''}`}>
        {/* Main sidebar content */}
        <div className="ai-sidebar-content">
          {/* Header */}
          <div className="flex-shrink-0 border-b border-gray-200 p-4 bg-gradient-to-r from-blue-50 to-purple-50">
            <div className="flex items-center space-x-3 mb-4">
              <div className="p-2 bg-blue-100 rounded-full">
                <Bot className="h-5 w-5 text-blue-600" />
              </div>
              <div>
                <h3 className="font-semibold text-gray-900">AI Assistant</h3>
                <p className="text-sm text-gray-500">Research article generation</p>
              </div>
            </div>

            {/* Model Selection */}
            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-700">AI Model</label>
              <Select value={selectedModel} onValueChange={setSelectedModel}>
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Select AI model" />
                </SelectTrigger>
                <SelectContent className="z-[10002]" sideOffset={5}>
                  <div className="p-2">
                    <div className="text-xs font-semibold text-gray-500 mb-1">FAST MODELS</div>
                    {getModelsByCategory('fast').map((model) => (
                      <SelectItem key={model.id} value={model.id}>
                        <div className="flex items-center justify-between w-full">
                          <span>{model.name}</span>
                          <Badge variant="outline" className="ml-2 text-xs">
                            {model.cost}
                          </Badge>
                        </div>
                      </SelectItem>
                    ))}
                  </div>
                  <div className="p-2">
                    <div className="text-xs font-semibold text-gray-500 mb-1">BALANCED</div>
                    {getModelsByCategory('balanced').map((model) => (
                      <SelectItem key={model.id} value={model.id}>
                        <div className="flex items-center justify-between w-full">
                          <span>{model.name}</span>
                          <Badge variant="outline" className="ml-2 text-xs">
                            {model.cost}
                          </Badge>
                        </div>
                      </SelectItem>
                    ))}
                  </div>
                  <div className="p-2">
                    <div className="text-xs font-semibold text-gray-500 mb-1">PREMIUM</div>
                    {getModelsByCategory('premium').map((model) => (
                      <SelectItem key={model.id} value={model.id}>
                        <div className="flex items-center justify-between w-full">
                          <span>{model.name}</span>
                          <Badge variant="outline" className="ml-2 text-xs">
                            {model.cost}
                          </Badge>
                        </div>
                      </SelectItem>
                    ))}
                  </div>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Main Content Area */}
          <div className="flex-1 flex flex-col h-full">
            <ScrollArea className="flex-1">
              <div className="p-4 space-y-4">

                {/* Primary AI Prompt Input */}
                <Card className="p-4 bg-gradient-to-r from-purple-50 to-indigo-50 border-purple-200">
                  <div className="flex items-center gap-2 mb-3">
                    <Wand2 className="h-5 w-5 text-purple-600" />
                    <span className="font-semibold text-purple-800">AI Assistant</span>
                  </div>
                  <div className="space-y-3">
                    <Textarea
                      value={customPrompt}
                      onChange={(e) => setCustomPrompt(e.target.value)}
                      placeholder="Ask AI to help with your research article... (e.g., 'Write an introduction about climate change', 'Improve this paragraph', 'Generate a literature review')"
                      className="w-full text-sm min-h-[100px] resize-none border-purple-200 focus:border-purple-400"
                      disabled={aiLoading}
                    />
                    <div className="flex gap-2">
                      <Button
                        size="sm"
                        className="flex-1 bg-purple-600 hover:bg-purple-700"
                        disabled={!customPrompt.trim() || aiLoading}
                        onClick={handleCustomPromptExecution}
                      >
                        {aiLoading ? (
                          <div className="h-4 w-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                        ) : (
                          <Send className="h-4 w-4 mr-2" />
                        )}
                        Generate
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setCustomPrompt('')}
                        disabled={!customPrompt.trim() || aiLoading}
                      >
                        Clear
                      </Button>
                    </div>
                  </div>
                </Card>

                {/* Quick Actions for Selected Text */}
                {selectedText && (
                  <Collapsible open={showQuickActions} onOpenChange={setShowQuickActions}>
                    <CollapsibleTrigger asChild>
                      <Card className="p-3 bg-blue-50 border-blue-200 cursor-pointer hover:bg-blue-100 transition-colors">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            <Edit className="h-4 w-4 text-blue-600" />
                            <span className="font-medium text-blue-800">Quick Actions</span>
                            <Badge variant="secondary" className="text-xs">
                              {selectedText.length} chars
                            </Badge>
                          </div>
                          {showQuickActions ? <ChevronUp className="h-4 w-4 text-blue-600" /> : <ChevronDown className="h-4 w-4 text-blue-600" />}
                        </div>
                      </Card>
                    </CollapsibleTrigger>
                    <CollapsibleContent>
                      <Card className="mt-2 p-3 border-blue-200">
                        <div className="grid grid-cols-2 gap-2">
                          {getToolsByCategory('enhancement').filter(tool =>
                            ['improve-clarity', 'fix-grammar', 'academic-tone', 'expand-point'].includes(tool.id)
                          ).map((tool) => (
                            <Button
                              key={tool.id}
                              variant="outline"
                              size="sm"
                              className="h-auto p-2 text-xs justify-start"
                              onClick={() => handleToolExecution(tool)}
                              disabled={aiLoading}
                            >
                              {iconMap[tool.icon] || <Sparkles className="h-4 w-4" />}
                              <span className="ml-1">{tool.name}</span>
                            </Button>
                          ))}
                        </div>
                      </Card>
                    </CollapsibleContent>
                  </Collapsible>
                )}


                {/* Content Generation Tools */}
                <Collapsible open={showContentGeneration} onOpenChange={setShowContentGeneration}>
                  <CollapsibleTrigger asChild>
                    <Card className="p-3 cursor-pointer hover:bg-gray-50 transition-colors">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <Sparkles className="h-4 w-4 text-green-600" />
                          <span className="font-medium text-gray-800">Content Generation</span>
                          <Badge variant="outline" className="text-xs">
                            {getToolsByCategory('generation').length} tools
                          </Badge>
                        </div>
                        {showContentGeneration ? <ChevronUp className="h-4 w-4 text-gray-600" /> : <ChevronDown className="h-4 w-4 text-gray-600" />}
                      </div>
                    </Card>
                  </CollapsibleTrigger>
                  <CollapsibleContent>
                    <Card className="mt-2 p-3">
                      <div className="grid grid-cols-1 gap-2">
                        {getToolsByCategory('generation').map((tool) => (
                          <Button
                            key={tool.id}
                            variant="outline"
                            size="sm"
                            className="h-auto p-3 justify-start text-left"
                            onClick={() => handleToolExecution(tool)}
                            disabled={aiLoading || (tool.requiresSelection && !selectedText)}
                          >
                            <div className="flex items-start gap-3 w-full">
                              <div className="p-1 bg-green-100 rounded flex-shrink-0">
                                {iconMap[tool.icon] || <Sparkles className="h-4 w-4 text-green-600" />}
                              </div>
                              <div className="flex-1 min-w-0">
                                <div className="font-medium text-sm">{tool.name}</div>
                                <p className="text-xs text-gray-600 mt-1">{tool.description}</p>
                                {tool.requiresSelection && !selectedText && (
                                  <p className="text-xs text-amber-600 mt-1">⚠️ Select text to use this tool</p>
                                )}
                              </div>
                            </div>
                          </Button>
                        ))}
                      </div>
                    </Card>
                  </CollapsibleContent>
                </Collapsible>

                {/* Text Enhancement Tools */}
                <Collapsible open={showTextEnhancement} onOpenChange={setShowTextEnhancement}>
                  <CollapsibleTrigger asChild>
                    <Card className="p-3 cursor-pointer hover:bg-gray-50 transition-colors">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <PenTool className="h-4 w-4 text-blue-600" />
                          <span className="font-medium text-gray-800">Text Enhancement</span>
                          <Badge variant="outline" className="text-xs">
                            {getToolsByCategory('enhancement').length} tools
                          </Badge>
                        </div>
                        {showTextEnhancement ? <ChevronUp className="h-4 w-4 text-gray-600" /> : <ChevronDown className="h-4 w-4 text-gray-600" />}
                      </div>
                    </Card>
                  </CollapsibleTrigger>
                  <CollapsibleContent>
                    <Card className="mt-2 p-3">
                      <div className="grid grid-cols-1 gap-2">
                        {getToolsByCategory('enhancement').map((tool) => (
                          <Button
                            key={tool.id}
                            variant="outline"
                            size="sm"
                            className="h-auto p-3 justify-start text-left"
                            onClick={() => handleToolExecution(tool)}
                            disabled={aiLoading || (tool.requiresSelection && !selectedText)}
                          >
                            <div className="flex items-start gap-3 w-full">
                              <div className="p-1 bg-blue-100 rounded flex-shrink-0">
                                {iconMap[tool.icon] || <PenTool className="h-4 w-4 text-blue-600" />}
                              </div>
                              <div className="flex-1 min-w-0">
                                <div className="font-medium text-sm">{tool.name}</div>
                                <p className="text-xs text-gray-600 mt-1">{tool.description}</p>
                                {tool.requiresSelection && !selectedText && (
                                  <p className="text-xs text-amber-600 mt-1">⚠️ Select text to use this tool</p>
                                )}
                              </div>
                            </div>
                          </Button>
                        ))}
                      </div>
                    </Card>
                  </CollapsibleContent>
                </Collapsible>

                {/* Research Tools */}
                <Collapsible open={showResearchTools} onOpenChange={setShowResearchTools}>
                  <CollapsibleTrigger asChild>
                    <Card className="p-3 cursor-pointer hover:bg-gray-50 transition-colors">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <Search className="h-4 w-4 text-purple-600" />
                          <span className="font-medium text-gray-800">Research Tools</span>
                          <Badge variant="outline" className="text-xs">
                            {getToolsByCategory('analysis').length} tools
                          </Badge>
                        </div>
                        {showResearchTools ? <ChevronUp className="h-4 w-4 text-gray-600" /> : <ChevronDown className="h-4 w-4 text-gray-600" />}
                      </div>
                    </Card>
                  </CollapsibleTrigger>
                  <CollapsibleContent>
                    <Card className="mt-2 p-3">
                      <div className="grid grid-cols-1 gap-2">
                        {getToolsByCategory('analysis').map((tool) => (
                          <Button
                            key={tool.id}
                            variant="outline"
                            size="sm"
                            className="h-auto p-3 justify-start text-left"
                            onClick={() => handleToolExecution(tool)}
                            disabled={aiLoading || (tool.requiresSelection && !selectedText)}
                          >
                            <div className="flex items-start gap-3 w-full">
                              <div className="p-1 bg-purple-100 rounded flex-shrink-0">
                                {iconMap[tool.icon] || <Search className="h-4 w-4 text-purple-600" />}
                              </div>
                              <div className="flex-1 min-w-0">
                                <div className="font-medium text-sm">{tool.name}</div>
                                <p className="text-xs text-gray-600 mt-1">{tool.description}</p>
                                {tool.requiresSelection && !selectedText && (
                                  <p className="text-xs text-amber-600 mt-1">⚠️ Select text to use this tool</p>
                                )}
                              </div>
                            </div>
                          </Button>
                        ))}
                      </div>
                    </Card>
                  </CollapsibleContent>
                </Collapsible>

                {/* Document Statistics */}
                <Card className="p-4 bg-gray-50">
                  <h3 className="font-semibold mb-3 flex items-center gap-2">
                    <BarChart3 className="h-4 w-4" />
                    Document Stats
                  </h3>
                  <div className="grid grid-cols-2 gap-3">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-blue-600">{wordCount.toLocaleString()}</div>
                      <div className="text-xs text-gray-500">Words</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-green-600">{characterCount.toLocaleString()}</div>
                      <div className="text-xs text-gray-500">Characters</div>
                    </div>
                  </div>
                  <div className="mt-3 pt-3 border-t border-gray-200">
                    <div className="flex items-center gap-2 text-sm">
                      {status === 'saved' && <CheckCircle className="h-4 w-4 text-green-500" />}
                      {status === 'saving' && <div className="h-4 w-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin" />}
                      {status === 'error' && <AlertCircle className="h-4 w-4 text-red-500" />}
                      {status === 'unsaved' && <AlertCircle className="h-4 w-4 text-amber-500" />}
                      <span className="capitalize">{status}</span>
                    </div>
                    {lastSaved && (
                      <div className="text-xs text-gray-500 mt-1">
                        Last saved: {lastSaved.toLocaleTimeString()}
                      </div>
                    )}
                  </div>
                </Card>

              </div>
            </ScrollArea>
          </div>
        </div>
      </div>
    </>
  );
}
